# Web Pages Analyzer Makefile
# This Makefile provides commands to build, test, and run the web pages analyzer service

# Variables
BINARY_NAME=web-analyzer
MAIN_PATH=.
PORT=8080

# Go related variables
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Colors for output
RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[1;33m
BLUE=\033[0;34m
NC=\033[0m # No Color

.PHONY: help build run test test-verbose test-coverage clean deps tidy check-deps install-tools generate-mocks

# Default target
help: ## Show this help message
	@echo "$(BLUE)Web Pages Analyzer - Available Commands:$(NC)"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}'
	@echo ""
	@echo "$(YELLOW)Examples:$(NC)"
	@echo "  make run          # Start the web service on port $(PORT)"
	@echo "  make test         # Run all unit tests"
	@echo "  make test-coverage # Run tests with coverage report"

# Build the application
build: ## Build the web analyzer binary
	@echo "$(BLUE)Building $(BINARY_NAME)...$(NC)"
	$(GOBUILD) -o $(BINARY_NAME) $(MAIN_PATH)
	@echo "$(GREEN)Build completed: $(BINARY_NAME)$(NC)"

# Run the service
run: build ## Build and run the web service
	@echo "$(BLUE)Starting web pages analyzer service on port $(PORT)...$(NC)"
	@echo "$(YELLOW)Access the web interface at: http://localhost:$(PORT)$(NC)"
	@echo "$(YELLOW)API endpoint: http://localhost:$(PORT)/api/analyze$(NC)"
	@echo "$(YELLOW)Press Ctrl+C to stop the service$(NC)"
	@echo ""
	./$(BINARY_NAME)

# Run without building (assumes binary exists)
start: ## Start the service (assumes binary is already built)
	@echo "$(BLUE)Starting web pages analyzer service...$(NC)"
	@if [ ! -f $(BINARY_NAME) ]; then \
		echo "$(RED)Binary not found. Run 'make build' first.$(NC)"; \
		exit 1; \
	fi
	./$(BINARY_NAME)

# Run all unit tests
test: ## Run all unit tests
	@echo "$(BLUE)Running all unit tests...$(NC)"
	$(GOTEST) ./internal/... -v
	@echo "$(GREEN)All tests completed!$(NC)"

# Run tests with verbose output
test-verbose: ## Run all unit tests with verbose output
	@echo "$(BLUE)Running all unit tests (verbose)...$(NC)"
	$(GOTEST) ./internal/... -v -count=1
	@echo "$(GREEN)All tests completed!$(NC)"

# Run tests with coverage
test-coverage: ## Run all unit tests with coverage report
	@echo "$(BLUE)Running all unit tests with coverage...$(NC)"
	@echo ""
	@echo "$(YELLOW)HTTP Client Tests:$(NC)"
	$(GOTEST) ./internal/infrastructure/clients/http -cover
	@echo ""
	@echo "$(YELLOW)HTML Parser Tests:$(NC)"
	$(GOTEST) ./internal/infrastructure/html_parser -cover
	@echo ""
	@echo "$(YELLOW)Webpage Analyzer Use Case Tests:$(NC)"
	$(GOTEST) ./internal/usecases/webpage_analyzer -cover
	@echo ""
	@echo "$(YELLOW)Controller Tests:$(NC)"
	$(GOTEST) ./internal/controllers/webpage_analyzer -cover
	@echo ""
	@echo "$(GREEN)Coverage analysis completed!$(NC)"

# Run tests for specific components
test-http: ## Run HTTP client tests only
	@echo "$(BLUE)Running HTTP client tests...$(NC)"
	$(GOTEST) ./internal/infrastructure/clients/http -v -cover

test-parser: ## Run HTML parser tests only
	@echo "$(BLUE)Running HTML parser tests...$(NC)"
	$(GOTEST) ./internal/infrastructure/html_parser -v -cover

test-analyzer: ## Run webpage analyzer use case tests only
	@echo "$(BLUE)Running webpage analyzer use case tests...$(NC)"
	$(GOTEST) ./internal/usecases/webpage_analyzer -v -cover

test-controller: ## Run controller tests only
	@echo "$(BLUE)Running controller tests...$(NC)"
	$(GOTEST) ./internal/controllers/webpage_analyzer -v -cover

# Clean build artifacts
clean: ## Clean build artifacts and temporary files
	@echo "$(BLUE)Cleaning build artifacts...$(NC)"
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	@echo "$(GREEN)Clean completed!$(NC)"

# Download dependencies
deps: ## Download and install dependencies
	@echo "$(BLUE)Downloading dependencies...$(NC)"
	$(GOGET) -d ./...
	@echo "$(GREEN)Dependencies downloaded!$(NC)"

# Tidy up go.mod and go.sum
tidy: ## Tidy up go.mod and go.sum files
	@echo "$(BLUE)Tidying up go modules...$(NC)"
	$(GOMOD) tidy
	@echo "$(GREEN)Go modules tidied!$(NC)"

# Check if all dependencies are available
check-deps: ## Check if all dependencies are available
	@echo "$(BLUE)Checking dependencies...$(NC)"
	$(GOMOD) verify
	@echo "$(GREEN)All dependencies verified!$(NC)"

# Install development tools
install-tools: ## Install development tools (mockgen, etc.)
	@echo "$(BLUE)Installing development tools...$(NC)"
	$(GOGET) go.uber.org/mock/mockgen@latest
	@echo "$(GREEN)Development tools installed!$(NC)"

# Generate mocks (if needed to regenerate)
generate-mocks: ## Regenerate all mock files
	@echo "$(BLUE)Generating mock files...$(NC)"
	@echo "$(YELLOW)Generating HTTP client mock...$(NC)"
	mockgen -source=internal/domain/clients/http/client.go -destination=internal/infrastructure/clients/http/mocks/mock_http_client.go -package=mocks
	@echo "$(YELLOW)Generating HTML parser mock...$(NC)"
	mockgen -source=internal/domain/html/parser.go -destination=internal/infrastructure/html_parser/mocks/mock_html_parser.go -package=mocks
	@echo "$(YELLOW)Generating parser factory mock...$(NC)"
	mockgen -source=internal/domain/html/parser_factory.go -destination=internal/infrastructure/html_parser/mocks/mock_parser_factory.go -package=mocks
	@echo "$(YELLOW)Generating webpage analyzer mock...$(NC)"
	mockgen -source=internal/domain/webpage/page.go -destination=internal/usecases/webpage_analyzer/mocks/mock_analyzer.go -package=mocks
	@echo "$(GREEN)All mocks generated!$(NC)"

# Development workflow commands
dev-setup: deps install-tools ## Setup development environment
	@echo "$(GREEN)Development environment setup completed!$(NC)"

dev-test: clean test-coverage ## Clean and run full test suite
	@echo "$(GREEN)Development testing completed!$(NC)"

dev-run: clean build run ## Clean, build, and run the service
	@echo "$(GREEN)Development run completed!$(NC)"

# Quick commands for common workflows
quick-test: ## Quick test run (no coverage)
	@echo "$(BLUE)Running quick tests...$(NC)"
	$(GOTEST) ./internal/... -short
	@echo "$(GREEN)Quick tests completed!$(NC)"

quick-build: ## Quick build without cleaning
	@echo "$(BLUE)Quick build...$(NC)"
	$(GOBUILD) -o $(BINARY_NAME) $(MAIN_PATH)
	@echo "$(GREEN)Quick build completed!$(NC)"

# Docker related commands (if Dockerfile exists)
docker-build: ## Build Docker image
	@echo "$(BLUE)Building Docker image...$(NC)"
	docker build -t web-analyzer .
	@echo "$(GREEN)Docker image built!$(NC)"

docker-run: ## Run Docker container
	@echo "$(BLUE)Running Docker container...$(NC)"
	docker run -p $(PORT):$(PORT) web-analyzer

# Show project status
status: ## Show project status and information
	@echo "$(BLUE)Web Pages Analyzer - Project Status$(NC)"
	@echo ""
	@echo "$(YELLOW)Go Version:$(NC)"
	@go version
	@echo ""
	@echo "$(YELLOW)Project Structure:$(NC)"
	@echo "  Binary: $(BINARY_NAME)"
	@echo "  Port: $(PORT)"
	@echo "  Main: $(MAIN_PATH)"
	@echo ""
	@echo "$(YELLOW)Available Tests:$(NC)"
	@echo "  - HTTP Client (infrastructure layer)"
	@echo "  - HTML Parser (infrastructure layer)"
	@echo "  - Webpage Analyzer (use case layer)"
	@echo "  - Controller (presentation layer)"
	@echo ""
	@if [ -f $(BINARY_NAME) ]; then \
		echo "$(GREEN)✓ Binary exists$(NC)"; \
	else \
		echo "$(RED)✗ Binary not built$(NC)"; \
	fi

# Default target when no target is specified
.DEFAULT_GOAL := help
