# Makefile Documentation

This Makefile provides comprehensive commands for building, testing, and running the Web Pages Analyzer service.

## Quick Start

### Run the Service
```bash
make run
```
This will build the binary and start the web service on port 8080.

### Run All Unit Tests
```bash
make test
```
This will execute all unit tests across all layers of the application.

## Available Commands

### Core Commands

#### `make run`
- **Purpose**: Build and run the web service
- **Port**: 8080
- **Access**: 
  - Web interface: http://localhost:8080
  - API endpoint: http://localhost:8080/api/analyze
- **Dependencies**: Automatically builds the binary if needed

#### `make test`
- **Purpose**: Run all unit tests
- **Coverage**: Tests all layers (infrastructure, use case, controller)
- **Output**: Verbose test results for all components

#### `make test-coverage`
- **Purpose**: Run all tests with coverage analysis
- **Output**: Coverage percentage for each component:
  - HTTP Client: 100.0% coverage
  - HTML Parser: 93.3% coverage  
  - Webpage Analyzer Use Case: 100.0% coverage
  - Controller: 96.0% coverage

### Build Commands

#### `make build`
- **Purpose**: Build the web-analyzer binary
- **Output**: Creates `web-analyzer` executable
- **Clean**: Use `make clean` first to remove old builds

#### `make clean`
- **Purpose**: Remove build artifacts and temporary files
- **Removes**: Binary files, temporary build files

#### `make quick-build`
- **Purpose**: Fast build without cleaning
- **Use Case**: Development iterations

### Test Commands

#### `make test-verbose`
- **Purpose**: Run tests with detailed verbose output
- **Use Case**: Debugging test failures

#### `make quick-test`
- **Purpose**: Run tests without coverage (faster)
- **Use Case**: Quick validation during development

#### Component-Specific Tests:
- `make test-http` - HTTP client tests only
- `make test-parser` - HTML parser tests only  
- `make test-analyzer` - Use case tests only
- `make test-controller` - Controller tests only

### Development Commands

#### `make dev-setup`
- **Purpose**: Setup complete development environment
- **Actions**: Downloads dependencies, installs tools (mockgen)

#### `make dev-test`
- **Purpose**: Clean build and full test suite
- **Use Case**: Pre-commit validation

#### `make dev-run`
- **Purpose**: Clean, build, and run service
- **Use Case**: Fresh development start

### Dependency Management

#### `make deps`
- **Purpose**: Download and install Go dependencies
- **Use Case**: Initial setup or dependency updates

#### `make tidy`
- **Purpose**: Clean up go.mod and go.sum files
- **Use Case**: Dependency maintenance

#### `make check-deps`
- **Purpose**: Verify all dependencies are available
- **Use Case**: Environment validation

### Development Tools

#### `make install-tools`
- **Purpose**: Install development tools (mockgen, etc.)
- **Required For**: Mock generation and testing

#### `make generate-mocks`
- **Purpose**: Regenerate all mock files
- **Generates**:
  - HTTP client mock
  - HTML parser mock
  - Parser factory mock
  - Webpage analyzer mock

### Docker Commands

#### `make docker-build`
- **Purpose**: Build Docker image
- **Image Name**: web-analyzer

#### `make docker-run`
- **Purpose**: Run service in Docker container
- **Port**: Maps to 8080

### Utility Commands

#### `make status`
- **Purpose**: Show project status and information
- **Output**: Go version, project structure, binary status

#### `make help`
- **Purpose**: Show all available commands with descriptions
- **Default**: Runs automatically when no target specified

## Usage Examples

### Development Workflow
```bash
# Initial setup
make dev-setup

# Development cycle
make dev-test          # Test everything
make dev-run           # Run service

# Quick iterations
make quick-test        # Fast test
make quick-build       # Fast build
```

### Testing Workflow
```bash
# Full test suite
make test-coverage

# Component testing
make test-http         # Test HTTP layer
make test-parser       # Test parser layer
make test-analyzer     # Test use case layer
make test-controller   # Test controller layer

# Debug specific issues
make test-verbose
```

### Production Workflow
```bash
# Clean build
make clean
make build

# Verify tests pass
make test

# Run service
make start
```

### Mock Management
```bash
# Install tools
make install-tools

# Regenerate mocks
make generate-mocks

# Test with new mocks
make test
```

## Configuration

### Environment Variables
- **PORT**: Service port (default: 8080)
- **BINARY_NAME**: Output binary name (default: web-analyzer)

### Customization
Edit the Makefile variables at the top:
```makefile
BINARY_NAME=web-analyzer
MAIN_PATH=.
PORT=8080
```

## Test Coverage Summary

| Component | Coverage | Test Count |
|-----------|----------|------------|
| HTTP Client | 100.0% | 15+ tests |
| HTML Parser | 93.3% | 20+ tests |
| Use Case | 100.0% | 15+ tests |
| Controller | 96.0% | 15+ tests |

## Dependencies

### Runtime Dependencies
- Go 1.21+
- Standard library packages

### Development Dependencies
- `go.uber.org/mock/gomock` - Mock generation and testing
- `net/http/httptest` - HTTP testing utilities

### Optional Dependencies
- Docker (for containerized deployment)

## Troubleshooting

### Common Issues

#### "Binary not found" when running `make start`
```bash
make build  # Build the binary first
```

#### Mock generation fails
```bash
make install-tools  # Install mockgen
```

#### Tests fail with import errors
```bash
make deps    # Download dependencies
make tidy    # Clean up modules
```

#### Port already in use
```bash
# Change port in Makefile or kill existing process
lsof -ti:8080 | xargs kill
```

## Integration with IDEs

### VS Code
Add to `.vscode/tasks.json`:
```json
{
    "label": "Run Tests",
    "type": "shell",
    "command": "make test",
    "group": "test"
}
```

### GoLand
Configure external tools with Makefile commands.

## Best Practices

1. **Always run tests before committing**: `make dev-test`
2. **Use coverage reports**: `make test-coverage`
3. **Clean builds for releases**: `make clean && make build`
4. **Keep dependencies updated**: `make deps && make tidy`
5. **Regenerate mocks after interface changes**: `make generate-mocks`

## Performance Notes

- `make quick-test` is faster for development iterations
- `make test-coverage` provides comprehensive analysis but takes longer
- Component-specific tests (`make test-http`, etc.) are fastest for focused testing
- Docker builds take longer but provide consistent environments
