# Web Page Analyzer Controller Tests

This directory contains comprehensive unit tests for the web page analyzer HTTP controller using mockgen-generated mocks for the WebPageAnalyzer use case dependency.

## Files Overview

- `analyzer.go` - The HTTP controller implementation
- `analyzer_test.go` - Unit tests for the controller's Analyze method
- `README.md` - This documentation file

## Running Tests

### Run Controller Tests
```bash
go test ./internal/controllers/webpage_analyzer -v
```

### Run with Coverage
```bash
go test ./internal/controllers/webpage_analyzer -cover
```

## Test Coverage

The test suite achieves **96.0% code coverage** and focuses specifically on testing the `Analyze()` method with comprehensive scenarios:

### Constructor Tests (`TestNew`)
- ✅ Dependency injection verification
- ✅ Controller instance creation

### Success Scenarios (`TestAnalyze_Success`)
- ✅ **HTML5 page analysis** - Complete analysis with all features
- ✅ **Simple page analysis** - Basic HTML structure
- ✅ **Complex link structure** - Advanced link analysis scenarios
- ✅ Proper JSON response formatting
- ✅ Content-Type header verification
- ✅ All analysis fields validation

### JSON Request Validation (`TestAnalyze_InvalidJSON`)
- ✅ **Malformed JSON** - Incomplete JSON syntax
- ✅ **Invalid JSON syntax** - Incorrect JSON format
- ✅ **Empty request body** - Missing content
- ✅ **Non-JSON content** - Plain text instead of JSON
- ✅ Proper 400 Bad Request responses
- ✅ Error message validation

### URL Validation (`TestAnalyze_URLValidationErrors`)
- ✅ **Empty URL** - Missing URL field
- ✅ **Invalid URL format** - Malformed URLs
- ✅ **Unsupported schemes** - FTP, file:// protocols
- ✅ **Missing host** - Incomplete URLs
- ✅ **Relative URLs** - Path-only URLs
- ✅ Proper 400 Bad Request responses
- ✅ Specific error message validation

### Use Case Error Handling (`TestAnalyze_AnalyzerError`)
- ✅ **Network errors** - Connection timeouts, unreachable hosts
- ✅ **Parsing errors** - HTML parsing failures
- ✅ **Generic errors** - Various analyzer failures
- ✅ Proper 500 Internal Server Error responses
- ✅ Error message propagation

## Mock Usage

The tests use the mock WebPageAnalyzer from `internal/usecases/webpage_analyzer/mocks/mock_analyzer.go`:

### Basic Mock Setup
```go
ctrl := gomock.NewController(t)
defer ctrl.Finish()

mockAnalyzer := mocks.NewMockWebPageAnalyzer(ctrl)
```

### Success Scenario Mock
```go
mockAnalyzer.EXPECT().
    Analyze("https://example.com").
    Return(&dmpg.WebPageAnalysis{
        HTMLVersion:  "HTML5",
        Title:        "Example Page",
        Headings:     map[string]int{"h1": 2, "h2": 3, "h3": 1, "h4": 0, "h5": 0, "h6": 0},
        Links:        dmhtml.LinkAnalysis{Internal: 5, External: 3, Inaccessible: 1},
        HasLoginForm: true,
    }, nil).
    Times(1)
```

### Error Scenario Mock
```go
mockAnalyzer.EXPECT().
    Analyze("https://unreachable.com").
    Return(nil, errors.New("network timeout")).
    Times(1)
```

## Key Testing Patterns

### 1. HTTP Request/Response Testing
```go
req := httptest.NewRequest(http.MethodPost, "/analyze", strings.NewReader(requestBody))
req.Header.Set("Content-Type", "application/json")
w := httptest.NewRecorder()

controller.Analyze(w, req)

// Verify status code, headers, and response body
```

### 2. Table-Driven Tests
All test functions use table-driven patterns for comprehensive coverage:
```go
tests := []struct {
    name           string
    requestBody    string
    url            string
    analysisResult *dmpg.WebPageAnalysis
}{
    // test cases...
}
```

### 3. JSON Request/Response Validation
```go
// Create JSON request
requestBody := map[string]string{"url": url}
jsonBody, _ := json.Marshal(requestBody)

// Parse JSON response
var result dmpg.WebPageAnalysis
json.NewDecoder(w.Body).Decode(&result)
```

### 4. Error Response Validation
```go
if w.Code != http.StatusBadRequest {
    t.Errorf("expected status %d, got %d", http.StatusBadRequest, w.Code)
}

responseBody := strings.TrimSpace(w.Body.String())
if !strings.Contains(responseBody, expectedError) {
    t.Errorf("expected error message to contain %q, got %q", expectedError, responseBody)
}
```

## Test Categories

### Input Validation Tests
- **JSON Parsing**: Validates proper JSON request handling
- **URL Validation**: Tests the `validateURL()` function thoroughly
- **Error Responses**: Ensures proper HTTP status codes and error messages

### Business Logic Tests
- **Mock Integration**: Verifies proper use case layer integration
- **Data Flow**: Tests complete request-to-response data flow
- **Error Propagation**: Ensures errors from use case layer are properly handled

### HTTP Protocol Tests
- **Content-Type Headers**: Validates proper JSON response headers
- **Status Codes**: Tests correct HTTP status code usage
- **Response Format**: Ensures consistent JSON response structure

## Dependencies

- `go.uber.org/mock/gomock` - Mock framework
- `net/http/httptest` - HTTP testing utilities
- `web-pages-analyzer/internal/usecases/webpage_analyzer/mocks` - WebPageAnalyzer mock
- `web-pages-analyzer/internal/domain/webpage` - Domain types and interfaces
- `web-pages-analyzer/internal/domain/html` - HTML analysis types

## Test Execution Results

All tests pass with 96.0% code coverage:
```
=== RUN   TestNew
=== RUN   TestAnalyze_Success
=== RUN   TestAnalyze_InvalidJSON
=== RUN   TestAnalyze_URLValidationErrors
=== RUN   TestAnalyze_AnalyzerError
PASS
ok      web-pages-analyzer/internal/controllers/webpage_analyzer    0.004s    coverage: 96.0% of statements
```

## Coverage Analysis

The 96% coverage includes:
- ✅ All success paths through the Analyze method
- ✅ All error handling paths (JSON, URL validation, analyzer errors)
- ✅ All HTTP response scenarios (200, 400, 500)
- ✅ Complete URL validation logic
- ✅ JSON encoding/decoding paths

The remaining 4% likely represents edge cases in error handling or unreachable code paths.

## Best Practices Demonstrated

1. **HTTP Testing**: Proper use of `httptest` for HTTP handler testing
2. **Mock Isolation**: Testing controller in isolation from use case implementation
3. **Comprehensive Error Testing**: Testing all error paths and edge cases
4. **JSON Handling**: Proper testing of JSON request/response processing
5. **Status Code Validation**: Ensuring correct HTTP semantics
6. **Table-Driven Tests**: Organized, maintainable test structure
7. **Boundary Testing**: Testing validation logic thoroughly

## Integration with Existing Test Suite

The controller tests integrate seamlessly with the existing test infrastructure:
- Uses the same mock WebPageAnalyzer from the use case layer
- Follows established testing patterns from other components
- Maintains consistency with the overall testing strategy
- Provides comprehensive coverage of the HTTP layer
