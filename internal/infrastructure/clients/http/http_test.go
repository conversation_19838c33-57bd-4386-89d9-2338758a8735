package http

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	clihttp "web-pages-analyzer/internal/domain/clients/http"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name string
		cfg  *clihttp.HttpClientCfg
	}{
		{
			name: "creates client with default config",
			cfg: &clihttp.HttpClientCfg{
				Timeout:      10,
				MaxRedirects: 5,
			},
		},
		{
			name: "creates client with custom timeout",
			cfg: &clihttp.HttpClientCfg{
				Timeout:      30,
				MaxRedirects: 3,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := New(tt.cfg)

			// Verify that client is not nil
			if client == nil {
				t.Fatal("expected client to be non-nil")
			}

			// Verify that client implements the interface
			_, ok := client.(clihttp.HttpClient)
			if !ok {
				t.Fatal("expected client to implement HttpClient interface")
			}

			// Verify internal http.Client configuration
			httpClient := client.(*httpClient)
			expectedTimeout := time.Duration(tt.cfg.Timeout) * time.Second
			if httpClient.httpClient.Timeout != expectedTimeout {
				t.Errorf("expected timeout %v, got %v", expectedTimeout, httpClient.httpClient.Timeout)
			}
		})
	}
}

func TestHttpClient_Get_Success(t *testing.T) {
	tests := []struct {
		name          string
		statusCode    int
		responseBody  string
		expectedError bool
	}{
		{
			name:          "successful GET request with 200",
			statusCode:    http.StatusOK,
			responseBody:  "<html><body>Test</body></html>",
			expectedError: false,
		},
		{
			name:          "successful GET request with 201",
			statusCode:    http.StatusCreated,
			responseBody:  "Created",
			expectedError: false,
		},
		{
			name:          "successful GET request with 299",
			statusCode:    299,
			responseBody:  "Success",
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if r.Method != http.MethodGet {
					t.Errorf("expected GET request, got %s", r.Method)
				}
				w.WriteHeader(tt.statusCode)
				w.Write([]byte(tt.responseBody))
			}))
			defer server.Close()

			// Create client
			cfg := &clihttp.HttpClientCfg{
				Timeout:      10,
				MaxRedirects: 5,
			}
			client := New(cfg)

			// Make request
			resp, err := client.Get(server.URL)

			// Verify results
			if tt.expectedError && err == nil {
				t.Fatal("expected error but got none")
			}
			if !tt.expectedError && err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if !tt.expectedError {
				if resp == nil {
					t.Fatal("expected response but got nil")
				}
				if resp.StatusCode != tt.statusCode {
					t.Errorf("expected status code %d, got %d", tt.statusCode, resp.StatusCode)
				}
				resp.Body.Close()
			}
		})
	}
}

func TestHttpClient_Get_ClientError(t *testing.T) {
	tests := []struct {
		name         string
		statusCode   int
		expectedCode int
	}{
		{
			name:         "client error 400",
			statusCode:   http.StatusBadRequest,
			expectedCode: http.StatusBadRequest,
		},
		{
			name:         "client error 404",
			statusCode:   http.StatusNotFound,
			expectedCode: http.StatusNotFound,
		},
		{
			name:         "server error 500",
			statusCode:   http.StatusInternalServerError,
			expectedCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.statusCode)
				w.Write([]byte("Error response"))
			}))
			defer server.Close()

			// Create client
			cfg := &clihttp.HttpClientCfg{
				Timeout:      10,
				MaxRedirects: 5,
			}
			client := New(cfg)

			// Make request
			resp, err := client.Get(server.URL)

			// Verify error
			if err == nil {
				t.Fatal("expected error but got none")
			}

			// Check that it's an HttpError by checking the error message format
			if !strings.Contains(err.Error(), fmt.Sprintf("status code %d", tt.expectedCode)) {
				t.Errorf("expected error to contain status code %d, got %s", tt.expectedCode, err.Error())
			}

			if !strings.Contains(err.Error(), "faliure in GET call") {
				t.Errorf("expected error message to contain 'faliure in GET call', got %s", err.Error())
			}

			if resp != nil {
				t.Error("expected nil response on error")
			}
		})
	}
}

func TestHttpClient_Get_NetworkError(t *testing.T) {
	// Create client
	cfg := &clihttp.HttpClientCfg{
		Timeout:      1, // Very short timeout
		MaxRedirects: 5,
	}
	client := New(cfg)

	// Make request to invalid URL
	resp, err := client.Get("http://invalid-url-that-does-not-exist.com")

	// Verify error
	if err == nil {
		t.Fatal("expected error but got none")
	}

	// Check that it's an HttpError by checking the error message format
	if !strings.Contains(err.Error(), fmt.Sprintf("status code %d", http.StatusBadGateway)) {
		t.Errorf("expected error to contain status code %d, got %s", http.StatusBadGateway, err.Error())
	}

	if !strings.Contains(err.Error(), "error in GET call") {
		t.Errorf("expected error message to contain 'error in GET call', got %s", err.Error())
	}

	if resp != nil {
		t.Error("expected nil response on error")
	}
}

func TestHttpClient_Head_Success(t *testing.T) {
	tests := []struct {
		name          string
		statusCode    int
		expectedError bool
	}{
		{
			name:          "successful HEAD request with 200",
			statusCode:    http.StatusOK,
			expectedError: false,
		},
		{
			name:          "successful HEAD request with 301",
			statusCode:    http.StatusMovedPermanently,
			expectedError: false,
		},
		{
			name:          "successful HEAD request with 399",
			statusCode:    399,
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				if r.Method != http.MethodHead {
					t.Errorf("expected HEAD request, got %s", r.Method)
				}
				w.WriteHeader(tt.statusCode)
			}))
			defer server.Close()

			// Create client
			cfg := &clihttp.HttpClientCfg{
				Timeout:      10,
				MaxRedirects: 5,
			}
			client := New(cfg)

			// Make request
			resp, err := client.Head(server.URL)

			// Verify results
			if tt.expectedError && err == nil {
				t.Fatal("expected error but got none")
			}
			if !tt.expectedError && err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if !tt.expectedError {
				if resp == nil {
					t.Fatal("expected response but got nil")
				}
				if resp.StatusCode != tt.statusCode {
					t.Errorf("expected status code %d, got %d", tt.statusCode, resp.StatusCode)
				}
				resp.Body.Close()
			}
		})
	}
}

func TestHttpClient_Head_ClientError(t *testing.T) {
	tests := []struct {
		name         string
		statusCode   int
		expectedCode int
	}{
		{
			name:         "client error 400",
			statusCode:   http.StatusBadRequest,
			expectedCode: http.StatusBadRequest,
		},
		{
			name:         "client error 404",
			statusCode:   http.StatusNotFound,
			expectedCode: http.StatusNotFound,
		},
		{
			name:         "server error 500",
			statusCode:   http.StatusInternalServerError,
			expectedCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.statusCode)
			}))
			defer server.Close()

			// Create client
			cfg := &clihttp.HttpClientCfg{
				Timeout:      10,
				MaxRedirects: 5,
			}
			client := New(cfg)

			// Make request
			resp, err := client.Head(server.URL)

			// Verify error
			if err == nil {
				t.Fatal("expected error but got none")
			}

			// Check that it's an HttpError by checking the error message format
			if !strings.Contains(err.Error(), fmt.Sprintf("status code %d", tt.expectedCode)) {
				t.Errorf("expected error to contain status code %d, got %s", tt.expectedCode, err.Error())
			}

			if !strings.Contains(err.Error(), "faliure in HEAD call") {
				t.Errorf("expected error message to contain 'faliure in HEAD call', got %s", err.Error())
			}

			if resp != nil {
				t.Error("expected nil response on error")
			}
		})
	}
}

func TestHttpClient_Head_NetworkError(t *testing.T) {
	// Create client
	cfg := &clihttp.HttpClientCfg{
		Timeout:      1, // Very short timeout
		MaxRedirects: 5,
	}
	client := New(cfg)

	// Make request to invalid URL
	resp, err := client.Head("http://invalid-url-that-does-not-exist.com")

	// Verify error
	if err == nil {
		t.Fatal("expected error but got none")
	}

	// Check that it's an HttpError by checking the error message format
	if !strings.Contains(err.Error(), fmt.Sprintf("status code %d", http.StatusBadGateway)) {
		t.Errorf("expected error to contain status code %d, got %s", http.StatusBadGateway, err.Error())
	}

	if !strings.Contains(err.Error(), "error in HEAD call") {
		t.Errorf("expected error message to contain 'error in HEAD call', got %s", err.Error())
	}

	if resp != nil {
		t.Error("expected nil response on error")
	}
}

func TestHttpClient_RedirectHandling(t *testing.T) {
	redirectCount := 0
	maxRedirects := 3

	// Create test server that redirects
	var server *httptest.Server
	server = httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		redirectCount++
		if redirectCount <= maxRedirects {
			w.Header().Set("Location", fmt.Sprintf("%s/redirect%d", server.URL, redirectCount))
			w.WriteHeader(http.StatusMovedPermanently)
		} else {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("Final destination"))
		}
	}))
	defer server.Close()

	// Test with max redirects allowing the request to succeed
	t.Run("redirects within limit", func(t *testing.T) {
		redirectCount = 0
		cfg := &clihttp.HttpClientCfg{
			Timeout:      10,
			MaxRedirects: maxRedirects + 1, // Allow one more than needed
		}
		client := New(cfg)

		resp, err := client.Get(server.URL)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}
		if resp == nil {
			t.Fatal("expected response but got nil")
		}
		resp.Body.Close()
	})

	// Test with max redirects exceeded
	t.Run("redirects exceed limit", func(t *testing.T) {
		redirectCount = 0
		cfg := &clihttp.HttpClientCfg{
			Timeout:      10,
			MaxRedirects: maxRedirects - 1, // Less than needed
		}
		client := New(cfg)

		resp, err := client.Get(server.URL)
		// When redirects are exceeded, the client returns the last redirect response
		// which is a 301, and our httpClient treats 3xx as error for GET requests
		if err == nil {
			t.Fatal("expected error due to redirect response being treated as error")
		}
		if !strings.Contains(err.Error(), "faliure in GET call") {
			t.Errorf("expected error message to contain 'faliure in GET call', got %s", err.Error())
		}
		if resp != nil {
			t.Error("expected nil response on error")
		}
	})
}

func TestIsSucceed(t *testing.T) {
	tests := []struct {
		statusCode int
		expected   bool
	}{
		{199, false},
		{200, true},
		{201, true},
		{299, true},
		{300, false},
		{404, false},
		{500, false},
	}

	for _, tt := range tests {
		t.Run(fmt.Sprintf("status_%d", tt.statusCode), func(t *testing.T) {
			result := isSucceed(tt.statusCode)
			if result != tt.expected {
				t.Errorf("isSucceed(%d) = %v, expected %v", tt.statusCode, result, tt.expected)
			}
		})
	}
}

func TestIsHeadSucceed(t *testing.T) {
	tests := []struct {
		statusCode int
		expected   bool
	}{
		{199, false},
		{200, true},
		{201, true},
		{299, true},
		{300, true},
		{301, true},
		{399, true},
		{400, false},
		{404, false},
		{500, false},
	}

	for _, tt := range tests {
		t.Run(fmt.Sprintf("status_%d", tt.statusCode), func(t *testing.T) {
			result := isHeadSucceed(tt.statusCode)
			if result != tt.expected {
				t.Errorf("isHeadSucceed(%d) = %v, expected %v", tt.statusCode, result, tt.expected)
			}
		})
	}
}
