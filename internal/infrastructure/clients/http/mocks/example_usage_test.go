package mocks

import (
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"

	"go.uber.org/mock/gomock"

	clihttp "web-pages-analyzer/internal/domain/clients/http"
)

// Example function that uses HttpClient - this could be any component in your system
func fetchAndProcessData(client clihttp.HttpClient, url string) (string, error) {
	resp, err := client.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return strings.ToUpper(string(body)), nil
}

// Example function that checks if a URL is accessible
func isURLAccessible(client clihttp.HttpClient, url string) bool {
	resp, err := client.Head(url)
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	return resp.StatusCode >= 200 && resp.StatusCode < 400
}

func TestFetchAndProcessData_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockHttpClient(ctrl)

	// Create a mock response
	responseBody := "hello world"
	resp := &http.Response{
		StatusCode: 200,
		Body:       io.NopCloser(strings.NewReader(responseBody)),
	}

	// Set up expectations
	mockClient.EXPECT().
		Get("https://example.com").
		Return(resp, nil).
		Times(1)

	// Call the function under test
	result, err := fetchAndProcessData(mockClient, "https://example.com")

	// Verify results
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}

	expected := "HELLO WORLD"
	if result != expected {
		t.Errorf("expected %s, got %s", expected, result)
	}
}

func TestFetchAndProcessData_HttpError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockHttpClient(ctrl)

	// Set up expectations for error case
	expectedError := clihttp.NewHttpError(404, "Not Found")
	mockClient.EXPECT().
		Get("https://example.com/notfound").
		Return(nil, expectedError).
		Times(1)

	// Call the function under test
	result, err := fetchAndProcessData(mockClient, "https://example.com/notfound")

	// Verify results
	if err == nil {
		t.Fatal("expected error but got none")
	}

	if result != "" {
		t.Errorf("expected empty result on error, got %s", result)
	}

	if !strings.Contains(err.Error(), "404") {
		t.Errorf("expected error to contain '404', got %s", err.Error())
	}
}

func TestIsURLAccessible_Success(t *testing.T) {
	tests := []struct {
		name       string
		statusCode int
		expected   bool
	}{
		{"200 OK", 200, true},
		{"301 Moved", 301, true},
		{"399 Custom", 399, true},
		{"400 Bad Request", 400, false},
		{"404 Not Found", 404, false},
		{"500 Server Error", 500, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockClient := NewMockHttpClient(ctrl)

			// Create a mock response
			resp := &http.Response{
				StatusCode: tt.statusCode,
				Body:       io.NopCloser(strings.NewReader("")),
			}

			// Set up expectations
			mockClient.EXPECT().
				Head("https://example.com").
				Return(resp, nil).
				Times(1)

			// Call the function under test
			result := isURLAccessible(mockClient, "https://example.com")

			// Verify results
			if result != tt.expected {
				t.Errorf("expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestIsURLAccessible_NetworkError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockHttpClient(ctrl)

	// Set up expectations for network error
	networkError := errors.New("network error")
	mockClient.EXPECT().
		Head("https://invalid-url.com").
		Return(nil, networkError).
		Times(1)

	// Call the function under test
	result := isURLAccessible(mockClient, "https://invalid-url.com")

	// Verify results
	if result != false {
		t.Errorf("expected false on network error, got %v", result)
	}
}

func TestMockClient_MultipleCallsWithDifferentExpectations(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockHttpClient(ctrl)

	// Set up multiple expectations in order
	gomock.InOrder(
		mockClient.EXPECT().
			Head("https://example.com/check").
			Return(&http.Response{StatusCode: 200, Body: io.NopCloser(strings.NewReader(""))}, nil),
		mockClient.EXPECT().
			Get("https://example.com/data").
			Return(&http.Response{
				StatusCode: 200,
				Body:       io.NopCloser(strings.NewReader("test data")),
			}, nil),
	)

	// First call - check accessibility
	accessible := isURLAccessible(mockClient, "https://example.com/check")
	if !accessible {
		t.Error("expected URL to be accessible")
	}

	// Second call - fetch data
	data, err := fetchAndProcessData(mockClient, "https://example.com/data")
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if data != "TEST DATA" {
		t.Errorf("expected 'TEST DATA', got %s", data)
	}
}

func TestMockClient_CallCountVerification(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockHttpClient(ctrl)

	// Set up expectation for exactly 3 calls
	mockClient.EXPECT().
		Head(gomock.Any()).
		Return(&http.Response{StatusCode: 200, Body: io.NopCloser(strings.NewReader(""))}, nil).
		Times(3)

	// Make exactly 3 calls
	for i := 0; i < 3; i++ {
		isURLAccessible(mockClient, "https://example.com")
	}

	// The test will fail if we don't make exactly 3 calls
}

func TestMockClient_ArgumentMatching(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := NewMockHttpClient(ctrl)

	// Set up expectations with specific argument matching
	mockClient.EXPECT().
		Get("https://api.example.com/users").
		Return(&http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(strings.NewReader(`{"users": []}`)),
		}, nil)

	mockClient.EXPECT().
		Get("https://api.example.com/posts").
		Return(&http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(strings.NewReader(`{"posts": []}`)),
		}, nil)

	// Call with specific URLs
	usersData, err := fetchAndProcessData(mockClient, "https://api.example.com/users")
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if !strings.Contains(usersData, "USERS") {
		t.Errorf("expected users data to contain 'USERS', got %s", usersData)
	}

	postsData, err := fetchAndProcessData(mockClient, "https://api.example.com/posts")
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if !strings.Contains(postsData, "POSTS") {
		t.Errorf("expected posts data to contain 'POSTS', got %s", postsData)
	}
}
