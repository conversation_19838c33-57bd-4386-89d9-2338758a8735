# HTTP Client Tests and Mocks

This directory contains comprehensive unit tests for the HTTP client implementation and mock generation for testing components that depend on the HTTP client.

## Files Overview

- `http.go` - The HTTP client implementation
- `http_test.go` - Unit tests for the HTTP client implementation
- `mocks/mock_http_client.go` - Generated mock for the HttpClient interface
- `mocks/example_usage_test.go` - Examples showing how to use the mock in tests

## Running Tests

### Run HTTP Client Tests
```bash
go test ./internal/infrastructure/clients/http -v
```

### Run Mock Usage Examples
```bash
go test ./internal/infrastructure/clients/http/mocks -v
```

### Run All Tests
```bash
go test ./internal/infrastructure/clients/http/... -v
```

## Test Coverage

The `http_test.go` file provides comprehensive coverage for:

### Constructor Tests
- ✅ Client creation with different configurations
- ✅ Timeout configuration verification
- ✅ Interface implementation verification

### GET Method Tests
- ✅ Successful requests (200, 201, 299)
- ✅ Client errors (400, 404, 500)
- ✅ Network errors (connection failures)
- ✅ Error message format verification

### HEAD Method Tests
- ✅ Successful requests (200, 301, 399)
- ✅ Client errors (400, 404, 500)
- ✅ Network errors (connection failures)
- ✅ Different success criteria for HEAD vs GET

### Redirect Handling Tests
- ✅ Redirects within configured limit
- ✅ Redirects exceeding configured limit
- ✅ Proper redirect response handling

### Utility Function Tests
- ✅ `isSucceed()` function for GET requests (200-299)
- ✅ `isHeadSucceed()` function for HEAD requests (200-399)

## Mock Usage

The generated mock (`mocks/mock_http_client.go`) can be used to test any component that depends on the `HttpClient` interface.

### Basic Mock Setup
```go
ctrl := gomock.NewController(t)
defer ctrl.Finish()

mockClient := NewMockHttpClient(ctrl)
```

### Setting Expectations
```go
// Expect a GET call and return a response
mockClient.EXPECT().
    Get("https://example.com").
    Return(&http.Response{
        StatusCode: 200,
        Body: io.NopCloser(strings.NewReader("response body")),
    }, nil).
    Times(1)
```

### Advanced Mock Features

#### Multiple Calls with Order
```go
gomock.InOrder(
    mockClient.EXPECT().Head("https://example.com").Return(resp1, nil),
    mockClient.EXPECT().Get("https://example.com").Return(resp2, nil),
)
```

#### Argument Matching
```go
// Match any URL
mockClient.EXPECT().Get(gomock.Any()).Return(resp, nil)

// Match specific URL
mockClient.EXPECT().Get("https://api.example.com").Return(resp, nil)
```

#### Call Count Verification
```go
// Expect exactly 3 calls
mockClient.EXPECT().Head(gomock.Any()).Return(resp, nil).Times(3)

// Expect at least 1 call
mockClient.EXPECT().Head(gomock.Any()).Return(resp, nil).MinTimes(1)

// Expect at most 2 calls
mockClient.EXPECT().Head(gomock.Any()).Return(resp, nil).MaxTimes(2)
```

## Regenerating Mocks

If the `HttpClient` interface changes, regenerate the mock:

```bash
$(go env GOPATH)/bin/mockgen -source=internal/domain/clients/http/interface.go -destination=internal/infrastructure/clients/http/mocks/mock_http_client.go -package=mocks
```

## Testing Best Practices

1. **Use httptest.NewServer()** for integration-style tests that need real HTTP behavior
2. **Use mocks** for unit tests of components that depend on HttpClient
3. **Test error conditions** including network failures and HTTP errors
4. **Verify error message formats** to ensure proper error handling
5. **Test edge cases** like redirects, timeouts, and different status codes

## Example Test Patterns

### Testing a Component that Uses HttpClient
```go
func TestMyComponent(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    mockClient := NewMockHttpClient(ctrl)
    component := NewMyComponent(mockClient)
    
    // Set up expectations
    mockClient.EXPECT().
        Get("https://api.example.com").
        Return(&http.Response{StatusCode: 200, Body: ...}, nil)
    
    // Test the component
    result, err := component.DoSomething()
    
    // Verify results
    assert.NoError(t, err)
    assert.Equal(t, expectedResult, result)
}
```

### Testing Error Handling
```go
func TestMyComponent_ErrorHandling(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    mockClient := NewMockHttpClient(ctrl)
    component := NewMyComponent(mockClient)
    
    // Set up error expectation
    expectedError := clihttp.NewHttpError(500, "Internal Server Error")
    mockClient.EXPECT().
        Get(gomock.Any()).
        Return(nil, expectedError)
    
    // Test error handling
    result, err := component.DoSomething()
    
    // Verify error handling
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "500")
}
```

## Dependencies

- `go.uber.org/mock/gomock` - Mock framework
- `net/http/httptest` - HTTP testing utilities
- Standard library `net/http` - HTTP client functionality
