// Code generated by MockGen. DO NOT EDIT.
// Source: internal/domain/html/html.go
//
// Generated by this command:
//
//	mockgen -source=internal/domain/html/html.go -destination=internal/infrastructure/html_parser/mocks/mock_parser_html.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"
	html "web-pages-analyzer/internal/domain/html"

	gomock "go.uber.org/mock/gomock"
)

// MockHtmlParser is a mock of HtmlParser interface.
type MockHtmlParser struct {
	ctrl     *gomock.Controller
	recorder *MockHtmlParserMockRecorder
	isgomock struct{}
}

// MockHtmlParserMockRecorder is the mock recorder for MockHtmlParser.
type MockHtmlParserMockRecorder struct {
	mock *MockHtmlParser
}

// NewMockHtmlParser creates a new mock instance.
func NewMockHtmlParser(ctrl *gomock.Controller) *MockHtmlParser {
	mock := &MockHtmlParser{ctrl: ctrl}
	mock.recorder = &MockHtmlParserMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHtmlParser) EXPECT() *MockHtmlParserMockRecorder {
	return m.recorder
}

// AnalyzeLinks mocks base method.
func (m *MockHtmlParser) AnalyzeLinks() *html.LinkAnalysis {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnalyzeLinks")
	ret0, _ := ret[0].(*html.LinkAnalysis)
	return ret0
}

// AnalyzeLinks indicates an expected call of AnalyzeLinks.
func (mr *MockHtmlParserMockRecorder) AnalyzeLinks() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnalyzeLinks", reflect.TypeOf((*MockHtmlParser)(nil).AnalyzeLinks))
}

// CountHeadingLevels mocks base method.
func (m *MockHtmlParser) CountHeadingLevels() map[string]int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountHeadingLevels")
	ret0, _ := ret[0].(map[string]int)
	return ret0
}

// CountHeadingLevels indicates an expected call of CountHeadingLevels.
func (mr *MockHtmlParserMockRecorder) CountHeadingLevels() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountHeadingLevels", reflect.TypeOf((*MockHtmlParser)(nil).CountHeadingLevels))
}

// GetHtmlVersion mocks base method.
func (m *MockHtmlParser) GetHtmlVersion() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHtmlVersion")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetHtmlVersion indicates an expected call of GetHtmlVersion.
func (mr *MockHtmlParserMockRecorder) GetHtmlVersion() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHtmlVersion", reflect.TypeOf((*MockHtmlParser)(nil).GetHtmlVersion))
}

// GetTitle mocks base method.
func (m *MockHtmlParser) GetTitle() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTitle")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetTitle indicates an expected call of GetTitle.
func (mr *MockHtmlParserMockRecorder) GetTitle() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTitle", reflect.TypeOf((*MockHtmlParser)(nil).GetTitle))
}

// HasLoginForm mocks base method.
func (m *MockHtmlParser) HasLoginForm() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasLoginForm")
	ret0, _ := ret[0].(bool)
	return ret0
}

// HasLoginForm indicates an expected call of HasLoginForm.
func (mr *MockHtmlParserMockRecorder) HasLoginForm() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasLoginForm", reflect.TypeOf((*MockHtmlParser)(nil).HasLoginForm))
}
