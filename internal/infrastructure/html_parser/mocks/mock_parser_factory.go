// Code generated by MockGen. DO NOT EDIT.
// Source: internal/domain/html/parser_factory.go
//
// Generated by this command:
//
//	mockgen -source=internal/domain/html/parser_factory.go -destination=internal/infrastructure/html_parser/mocks/mock_parser_factory.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	io "io"
	reflect "reflect"
	http "web-pages-analyzer/internal/domain/clients/http"
	html "web-pages-analyzer/internal/domain/html"

	gomock "go.uber.org/mock/gomock"
)

// MockParserFactory is a mock of ParserFactory interface.
type MockParserFactory struct {
	ctrl     *gomock.Controller
	recorder *MockParserFactoryMockRecorder
	isgomock struct{}
}

// MockParserFactoryMockRecorder is the mock recorder for MockParserFactory.
type MockParserFactoryMockRecorder struct {
	mock *MockParserFactory
}

// NewMockParserFactory creates a new mock instance.
func NewMockParserFactory(ctrl *gomock.Controller) *MockParserFactory {
	mock := &MockParserFactory{ctrl: ctrl}
	mock.recorder = &MockParserFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockParserFactory) EXPECT() *MockParserFactoryMockRecorder {
	return m.recorder
}

// CreateParser mocks base method.
func (m *MockParserFactory) CreateParser(body io.Reader, baseUrl string, client http.HttpClient) (html.HtmlParser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateParser", body, baseUrl, client)
	ret0, _ := ret[0].(html.HtmlParser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateParser indicates an expected call of CreateParser.
func (mr *MockParserFactoryMockRecorder) CreateParser(body, baseUrl, client any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateParser", reflect.TypeOf((*MockParserFactory)(nil).CreateParser), body, baseUrl, client)
}
