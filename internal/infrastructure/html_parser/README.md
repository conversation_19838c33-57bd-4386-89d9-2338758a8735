# HTML Parser Tests

This directory contains comprehensive unit tests for the HTML parser implementation using mock HTTP clients generated with mockgen.

## Files Overview

- `parser.go` - The HTML parser implementation
- `parser_test.go` - Comprehensive unit tests for the parser
- `README.md` - This documentation file

## Running Tests

### Run Parser Tests
```bash
go test ./internal/infrastructure/html_parser -v
```

### Run with Coverage
```bash
go test ./internal/infrastructure/html_parser -cover
```

## Test Coverage

The test suite achieves **95.5% code coverage** and includes comprehensive testing for:

### Constructor Tests (`TestNew`)
- ✅ Valid HTML and URL combinations
- ✅ Invalid HTML with valid URL (html.Parse is forgiving)
- ✅ Valid HTML with invalid URL (error handling)
- ✅ Empty HTML content
- ✅ Parser field initialization verification

### HTML Version Detection (`TestGetHtmlVersion`)
- ✅ HTML5 doctype detection (`<!DOCTYPE html>`)
- ✅ Case-insensitive doctype parsing
- ✅ HTML 4.01 doctype detection
- ✅ XHTML doctype detection
- ✅ Unknown version for missing doctypes
- ✅ Empty content handling

### Title Extraction (`TestGetTitle`)
- ✅ Simple title extraction
- ✅ Title with leading/trailing whitespace
- ✅ Title with nested elements
- ✅ Missing title handling
- ✅ Empty title handling
- ✅ Multiple titles (first one wins)

### Heading Level Counting (`TestCountHeadingLevels`)
- ✅ Various heading levels (h1-h6)
- ✅ Multiple headings of same level
- ✅ No headings present
- ✅ Nested headings in complex HTML structure

### Login Form Detection (`TestHasLoginForm`)
- ✅ Form with login action attribute
- ✅ Form with signin class
- ✅ Password input with login-related names
- ✅ Form with username and password fields
- ✅ Forms without login indicators (negative test)
- ✅ No forms present (negative test)

### Link Analysis (`TestAnalyzeLinks`) - **Uses Mock HTTP Client**
- ✅ Mixed internal and external links
- ✅ All accessible links scenario
- ✅ Network errors making links inaccessible
- ✅ No links present
- ✅ Proper internal/external classification
- ✅ Accessibility checking via HTTP HEAD requests

### Link Accessibility Checking (`TestCheckLinkAccessibility`) - **Uses Mock HTTP Client**
- ✅ Accessible links (200, 301, 399 status codes)
- ✅ Inaccessible links (400, 404, 500 status codes)
- ✅ Network error handling
- ✅ Status code boundary testing (200-399 = accessible)

### Utility Function Tests
- ✅ **Link Extraction** (`TestExtractLinks`)
  - Various link types (internal, external, mailto, anchors, javascript)
  - Relative and absolute URL handling
  - Empty href and missing href attributes
  - No links scenarios

- ✅ **URL Resolution** (`TestResolveURL`)
  - Absolute URLs
  - Relative paths and files
  - Parent directory navigation (`../`)
  - Filtered URLs (anchors, javascript, mailto, tel)
  - Empty href handling

- ✅ **Internal Link Detection** (`TestIsInternalLink`)
  - Same host detection
  - Case-insensitive host comparison
  - Different host detection
  - Relative URLs (no host)
  - Subdomain handling
  - Different port handling

## Mock HTTP Client Usage

The tests demonstrate proper usage of the mock HTTP client generated with mockgen:

### Basic Mock Setup
```go
ctrl := gomock.NewController(t)
defer ctrl.Finish()

mockClient := mocks.NewMockHttpClient(ctrl)
```

### Setting Up HTTP Expectations
```go
// Expect successful HEAD request
mockClient.EXPECT().Head("https://example.com/page").Return(
    &http.Response{StatusCode: 200, Body: io.NopCloser(strings.NewReader(""))}, nil)

// Expect network error
mockClient.EXPECT().Head("https://unreachable.com").Return(
    nil, clihttp.NewHttpError(502, "Bad Gateway"))
```

### Complex Mock Scenarios
The tests include sophisticated mock setups for:
- Multiple concurrent HTTP requests (link analysis uses goroutines)
- Mixed success and failure responses
- Different status codes and error conditions
- Network timeouts and connection failures

## Key Testing Patterns

### 1. Table-Driven Tests
All test functions use table-driven patterns for comprehensive coverage:
```go
tests := []struct {
    name        string
    htmlContent string
    expected    interface{}
}{
    // test cases...
}
```

### 2. Mock HTTP Client Integration
Tests that require HTTP functionality use the generated mock:
```go
mockSetup: func(mock *mocks.MockHttpClient) {
    mock.EXPECT().Head(url).Return(response, error)
}
```

### 3. Concurrent Testing
Link analysis tests verify that concurrent HTTP requests work correctly with mocks.

### 4. Error Boundary Testing
Tests cover both success and failure scenarios, including edge cases.

## Dependencies

- `go.uber.org/mock/gomock` - Mock framework
- `golang.org/x/net/html` - HTML parsing
- `web-pages-analyzer/internal/infrastructure/clients/http/mocks` - Generated HTTP client mock

## Test Execution

All tests pass with 95.5% code coverage:
```
=== RUN   TestNew
=== RUN   TestGetHtmlVersion  
=== RUN   TestGetTitle
=== RUN   TestCountHeadingLevels
=== RUN   TestHasLoginForm
=== RUN   TestAnalyzeLinks
=== RUN   TestCheckLinkAccessibility
=== RUN   TestExtractLinks
=== RUN   TestResolveURL
=== RUN   TestIsInternalLink
PASS
ok      web-pages-analyzer/internal/infrastructure/html_parser    0.006s    coverage: 95.5% of statements
```

## Best Practices Demonstrated

1. **Comprehensive Mock Usage** - Proper setup and teardown of mock controllers
2. **Concurrent Testing** - Testing goroutine-based link analysis with mocks
3. **Error Handling** - Testing both success and failure scenarios
4. **Edge Case Coverage** - Testing boundary conditions and invalid inputs
5. **Clean Test Structure** - Table-driven tests with clear naming
6. **Realistic Test Data** - Using actual HTML structures in test cases
