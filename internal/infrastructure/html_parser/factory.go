package html_parser

import (
	"io"

	clihttp "web-pages-analyzer/internal/domain/clients/http"
	dmhtml "web-pages-analyzer/internal/domain/html"
)

type parserFactory struct{}

// NewParserFactory creates a new parser factory
func NewParserFactory() dmhtml.ParserFactory {
	return &parserFactory{}
}

// CreateParser creates a new HTML parser instance
func (pf *parserFactory) CreateParser(body io.Reader, baseUrl string, client clihttp.HttpClient) (dmhtml.HtmlParser, error) {
	return New(body, baseUrl, client)
}
