// Code generated by MockGen. DO NOT EDIT.
// Source: internal/domain/webpage/page.go
//
// Generated by this command:
//
//	mockgen -source=internal/domain/webpage/page.go -destination=internal/usecases/webpage_analyzer/mocks/mock_analyzer.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"
	webpage "web-pages-analyzer/internal/domain/webpage"

	gomock "go.uber.org/mock/gomock"
)

// MockWebPageAnalyzer is a mock of WebPageAnalyzer interface.
type MockWebPageAnalyzer struct {
	ctrl     *gomock.Controller
	recorder *MockWebPageAnalyzerMockRecorder
	isgomock struct{}
}

// MockWebPageAnalyzerMockRecorder is the mock recorder for MockWebPageAnalyzer.
type MockWebPageAnalyzerMockRecorder struct {
	mock *MockWebPageAnalyzer
}

// NewMockWebPageAnalyzer creates a new mock instance.
func NewMockWebPageAnalyzer(ctrl *gomock.Controller) *MockWebPageAnalyzer {
	mock := &MockWebPageAnalyzer{ctrl: ctrl}
	mock.recorder = &MockWebPageAnalyzerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWebPageAnalyzer) EXPECT() *MockWebPageAnalyzerMockRecorder {
	return m.recorder
}

// Analyze mocks base method.
func (m *MockWebPageAnalyzer) Analyze(url string) (*webpage.WebPageAnalysis, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Analyze", url)
	ret0, _ := ret[0].(*webpage.WebPageAnalysis)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Analyze indicates an expected call of Analyze.
func (mr *MockWebPageAnalyzerMockRecorder) Analyze(url any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Analyze", reflect.TypeOf((*MockWebPageAnalyzer)(nil).Analyze), url)
}
