package webpage_analyzer

import (
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"

	"go.uber.org/mock/gomock"

	clihttp "web-pages-analyzer/internal/domain/clients/http"
	dmhtml "web-pages-analyzer/internal/domain/html"
	httpmocks "web-pages-analyzer/internal/infrastructure/clients/http/mocks"
	htmlmocks "web-pages-analyzer/internal/infrastructure/html_parser/mocks"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "creates analyzer with dependencies",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockHttpClient := httpmocks.NewMockHttpClient(ctrl)
			mockParserFactory := htmlmocks.NewMockParserFactory(ctrl)

			analyzer := New(mockHttpClient, mockParserFactory)

			if analyzer == nil {
				t.Fatal("expected analyzer to be non-nil")
			}

			// Verify that analyzer implements the interface (already guaranteed by return type)
			// This test mainly verifies the constructor works correctly
		})
	}
}

func TestAnalyze_Success(t *testing.T) {
	tests := []struct {
		name                string
		url                 string
		responseBody        string
		expectedHTMLVersion string
		expectedTitle       string
		expectedHeadings    map[string]int
		expectedLinks       dmhtml.LinkAnalysis
		expectedLoginForm   bool
	}{
		{
			name:                "complete HTML page analysis",
			url:                 "https://example.com",
			responseBody:        "<html><head><title>Test Page</title></head><body><h1>Header</h1><form action='/login'><input type='password'/></form></body></html>",
			expectedHTMLVersion: "HTML5",
			expectedTitle:       "Test Page",
			expectedHeadings:    map[string]int{"h1": 1, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0},
			expectedLinks:       dmhtml.LinkAnalysis{Internal: 2, External: 1, Inaccessible: 0},
			expectedLoginForm:   true,
		},
		{
			name:                "simple HTML page",
			url:                 "https://test.com/page",
			responseBody:        "<!DOCTYPE html><html><head><title>Simple</title></head><body><h2>Content</h2></body></html>",
			expectedHTMLVersion: "HTML5",
			expectedTitle:       "Simple",
			expectedHeadings:    map[string]int{"h1": 0, "h2": 1, "h3": 0, "h4": 0, "h5": 0, "h6": 0},
			expectedLinks:       dmhtml.LinkAnalysis{Internal: 0, External: 0, Inaccessible: 0},
			expectedLoginForm:   false,
		},
		{
			name:                "HTML with multiple headings",
			url:                 "https://blog.example.com",
			responseBody:        "<html><head><title>Blog</title></head><body><h1>Main</h1><h2>Sub1</h2><h2>Sub2</h2><h3>Detail</h3></body></html>",
			expectedHTMLVersion: "Unknown HTML Version",
			expectedTitle:       "Blog",
			expectedHeadings:    map[string]int{"h1": 1, "h2": 2, "h3": 1, "h4": 0, "h5": 0, "h6": 0},
			expectedLinks:       dmhtml.LinkAnalysis{Internal: 1, External: 2, Inaccessible: 1},
			expectedLoginForm:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup mock HTTP client
			mockHttpClient := httpmocks.NewMockHttpClient(ctrl)
			mockHttpClient.EXPECT().
				Get(tt.url).
				Return(&http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(strings.NewReader(tt.responseBody)),
				}, nil).
				Times(1)

			// Setup mock parser factory and parser
			mockParserFactory := htmlmocks.NewMockParserFactory(ctrl)
			mockParser := htmlmocks.NewMockHtmlParser(ctrl)

			mockParserFactory.EXPECT().
				CreateParser(gomock.Any(), tt.url, mockHttpClient).
				Return(mockParser, nil).
				Times(1)

			// Setup parser method expectations
			mockParser.EXPECT().GetHtmlVersion().Return(tt.expectedHTMLVersion).Times(1)
			mockParser.EXPECT().GetTitle().Return(tt.expectedTitle).Times(1)
			mockParser.EXPECT().CountHeadingLevels().Return(tt.expectedHeadings).Times(1)
			mockParser.EXPECT().AnalyzeLinks().Return(&tt.expectedLinks).Times(1)
			mockParser.EXPECT().HasLoginForm().Return(tt.expectedLoginForm).Times(1)

			// Create analyzer and test
			analyzer := New(mockHttpClient, mockParserFactory)
			result, err := analyzer.Analyze(tt.url)

			// Verify results
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if result == nil {
				t.Fatal("expected result to be non-nil")
			}

			if result.HTMLVersion != tt.expectedHTMLVersion {
				t.Errorf("expected HTML version %q, got %q", tt.expectedHTMLVersion, result.HTMLVersion)
			}

			if result.Title != tt.expectedTitle {
				t.Errorf("expected title %q, got %q", tt.expectedTitle, result.Title)
			}

			if !equalHeadings(result.Headings, tt.expectedHeadings) {
				t.Errorf("expected headings %v, got %v", tt.expectedHeadings, result.Headings)
			}

			if result.Links.Internal != tt.expectedLinks.Internal {
				t.Errorf("expected %d internal links, got %d", tt.expectedLinks.Internal, result.Links.Internal)
			}

			if result.Links.External != tt.expectedLinks.External {
				t.Errorf("expected %d external links, got %d", tt.expectedLinks.External, result.Links.External)
			}

			if result.Links.Inaccessible != tt.expectedLinks.Inaccessible {
				t.Errorf("expected %d inaccessible links, got %d", tt.expectedLinks.Inaccessible, result.Links.Inaccessible)
			}

			if result.HasLoginForm != tt.expectedLoginForm {
				t.Errorf("expected login form %v, got %v", tt.expectedLoginForm, result.HasLoginForm)
			}
		})
	}
}

func TestAnalyze_HttpClientError(t *testing.T) {
	tests := []struct {
		name          string
		url           string
		httpError     error
		expectedError string
	}{
		{
			name:          "network error",
			url:           "https://unreachable.com",
			httpError:     clihttp.NewHttpError(502, "Bad Gateway"),
			expectedError: "Bad Gateway",
		},
		{
			name:          "404 not found",
			url:           "https://example.com/notfound",
			httpError:     clihttp.NewHttpError(404, "Not Found"),
			expectedError: "Not Found",
		},
		{
			name:          "connection timeout",
			url:           "https://slow.example.com",
			httpError:     errors.New("connection timeout"),
			expectedError: "connection timeout",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup mock HTTP client to return error
			mockHttpClient := httpmocks.NewMockHttpClient(ctrl)
			mockHttpClient.EXPECT().
				Get(tt.url).
				Return(nil, tt.httpError).
				Times(1)

			// Parser factory should not be called when HTTP client fails
			mockParserFactory := htmlmocks.NewMockParserFactory(ctrl)

			// Create analyzer and test
			analyzer := New(mockHttpClient, mockParserFactory)
			result, err := analyzer.Analyze(tt.url)

			// Verify error handling
			if err == nil {
				t.Fatal("expected error, got nil")
			}

			if result != nil {
				t.Fatal("expected result to be nil when error occurs")
			}

			if !strings.Contains(err.Error(), tt.expectedError) {
				t.Errorf("expected error to contain %q, got %q", tt.expectedError, err.Error())
			}
		})
	}
}

func TestAnalyze_ParserFactoryError(t *testing.T) {
	tests := []struct {
		name          string
		url           string
		responseBody  string
		parserError   error
		expectedError string
	}{
		{
			name:          "invalid HTML parsing error",
			url:           "https://example.com",
			responseBody:  "invalid html content",
			parserError:   errors.New("failed to parse HTML"),
			expectedError: "failed to parse HTML",
		},
		{
			name:          "invalid URL error",
			url:           "invalid-url",
			responseBody:  "<html><body>content</body></html>",
			parserError:   errors.New("invalid URL format"),
			expectedError: "invalid URL format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup mock HTTP client to return successful response
			mockHttpClient := httpmocks.NewMockHttpClient(ctrl)
			mockHttpClient.EXPECT().
				Get(tt.url).
				Return(&http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(strings.NewReader(tt.responseBody)),
				}, nil).
				Times(1)

			// Setup mock parser factory to return error
			mockParserFactory := htmlmocks.NewMockParserFactory(ctrl)
			mockParserFactory.EXPECT().
				CreateParser(gomock.Any(), tt.url, mockHttpClient).
				Return(nil, tt.parserError).
				Times(1)

			// Create analyzer and test
			analyzer := New(mockHttpClient, mockParserFactory)
			result, err := analyzer.Analyze(tt.url)

			// Verify error handling
			if err == nil {
				t.Fatal("expected error, got nil")
			}

			if result != nil {
				t.Fatal("expected result to be nil when error occurs")
			}

			if !strings.Contains(err.Error(), tt.expectedError) {
				t.Errorf("expected error to contain %q, got %q", tt.expectedError, err.Error())
			}
		})
	}
}

func TestAnalyze_EdgeCases(t *testing.T) {
	tests := []struct {
		name                string
		url                 string
		responseBody        string
		expectedHTMLVersion string
		expectedTitle       string
		expectedHeadings    map[string]int
		expectedLinks       dmhtml.LinkAnalysis
		expectedLoginForm   bool
	}{
		{
			name:                "empty HTML body",
			url:                 "https://empty.com",
			responseBody:        "",
			expectedHTMLVersion: "Unknown HTML Version",
			expectedTitle:       "",
			expectedHeadings:    map[string]int{"h1": 0, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0},
			expectedLinks:       dmhtml.LinkAnalysis{Internal: 0, External: 0, Inaccessible: 0},
			expectedLoginForm:   false,
		},
		{
			name:                "minimal HTML",
			url:                 "https://minimal.com",
			responseBody:        "<html></html>",
			expectedHTMLVersion: "Unknown HTML Version",
			expectedTitle:       "",
			expectedHeadings:    map[string]int{"h1": 0, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0},
			expectedLinks:       dmhtml.LinkAnalysis{Internal: 0, External: 0, Inaccessible: 0},
			expectedLoginForm:   false,
		},
		{
			name:                "complex page with all features",
			url:                 "https://complex.example.com/page",
			responseBody:        "<!DOCTYPE html><html><head><title>Complex Page</title></head><body><h1>Main</h1><h2>Sub</h2><h3>Detail</h3><form class='login-form'><input type='email' name='username'/><input type='password'/></form><a href='/internal'>Internal</a><a href='https://external.com'>External</a></body></html>",
			expectedHTMLVersion: "HTML5",
			expectedTitle:       "Complex Page",
			expectedHeadings:    map[string]int{"h1": 1, "h2": 1, "h3": 1, "h4": 0, "h5": 0, "h6": 0},
			expectedLinks:       dmhtml.LinkAnalysis{Internal: 5, External: 3, Inaccessible: 2},
			expectedLoginForm:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup mock HTTP client
			mockHttpClient := httpmocks.NewMockHttpClient(ctrl)
			mockHttpClient.EXPECT().
				Get(tt.url).
				Return(&http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(strings.NewReader(tt.responseBody)),
				}, nil).
				Times(1)

			// Setup mock parser factory and parser
			mockParserFactory := htmlmocks.NewMockParserFactory(ctrl)
			mockParser := htmlmocks.NewMockHtmlParser(ctrl)

			mockParserFactory.EXPECT().
				CreateParser(gomock.Any(), tt.url, mockHttpClient).
				Return(mockParser, nil).
				Times(1)

			// Setup parser method expectations
			mockParser.EXPECT().GetHtmlVersion().Return(tt.expectedHTMLVersion).Times(1)
			mockParser.EXPECT().GetTitle().Return(tt.expectedTitle).Times(1)
			mockParser.EXPECT().CountHeadingLevels().Return(tt.expectedHeadings).Times(1)
			mockParser.EXPECT().AnalyzeLinks().Return(&tt.expectedLinks).Times(1)
			mockParser.EXPECT().HasLoginForm().Return(tt.expectedLoginForm).Times(1)

			// Create analyzer and test
			analyzer := New(mockHttpClient, mockParserFactory)
			result, err := analyzer.Analyze(tt.url)

			// Verify results
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if result == nil {
				t.Fatal("expected result to be non-nil")
			}

			// Verify all fields are correctly populated
			if result.HTMLVersion != tt.expectedHTMLVersion {
				t.Errorf("expected HTML version %q, got %q", tt.expectedHTMLVersion, result.HTMLVersion)
			}

			if result.Title != tt.expectedTitle {
				t.Errorf("expected title %q, got %q", tt.expectedTitle, result.Title)
			}

			if !equalHeadings(result.Headings, tt.expectedHeadings) {
				t.Errorf("expected headings %v, got %v", tt.expectedHeadings, result.Headings)
			}

			if result.Links.Internal != tt.expectedLinks.Internal {
				t.Errorf("expected %d internal links, got %d", tt.expectedLinks.Internal, result.Links.Internal)
			}

			if result.Links.External != tt.expectedLinks.External {
				t.Errorf("expected %d external links, got %d", tt.expectedLinks.External, result.Links.External)
			}

			if result.Links.Inaccessible != tt.expectedLinks.Inaccessible {
				t.Errorf("expected %d inaccessible links, got %d", tt.expectedLinks.Inaccessible, result.Links.Inaccessible)
			}

			if result.HasLoginForm != tt.expectedLoginForm {
				t.Errorf("expected login form %v, got %v", tt.expectedLoginForm, result.HasLoginForm)
			}
		})
	}
}

func TestAnalyze_MockVerification(t *testing.T) {
	t.Run("verifies all mock calls are made exactly once", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		url := "https://verification.test"
		responseBody := "<html><head><title>Test</title></head><body><h1>Header</h1></body></html>"

		// Setup mock HTTP client with strict expectations
		mockHttpClient := httpmocks.NewMockHttpClient(ctrl)
		mockHttpClient.EXPECT().
			Get(url).
			Return(&http.Response{
				StatusCode: 200,
				Body:       io.NopCloser(strings.NewReader(responseBody)),
			}, nil).
			Times(1) // Exactly once

		// Setup mock parser factory and parser with strict expectations
		mockParserFactory := htmlmocks.NewMockParserFactory(ctrl)
		mockParser := htmlmocks.NewMockHtmlParser(ctrl)

		mockParserFactory.EXPECT().
			CreateParser(gomock.Any(), url, mockHttpClient).
			Return(mockParser, nil).
			Times(1) // Exactly once

		// Each parser method should be called exactly once
		mockParser.EXPECT().GetHtmlVersion().Return("HTML5").Times(1)
		mockParser.EXPECT().GetTitle().Return("Test").Times(1)
		mockParser.EXPECT().CountHeadingLevels().Return(map[string]int{"h1": 1, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0}).Times(1)
		mockParser.EXPECT().AnalyzeLinks().Return(&dmhtml.LinkAnalysis{Internal: 0, External: 0, Inaccessible: 0}).Times(1)
		mockParser.EXPECT().HasLoginForm().Return(false).Times(1)

		// Create analyzer and test
		analyzer := New(mockHttpClient, mockParserFactory)
		result, err := analyzer.Analyze(url)

		// Verify no errors and result is populated
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		if result == nil {
			t.Fatal("expected result to be non-nil")
		}

		// The gomock controller will automatically verify that all expectations were met
		// when ctrl.Finish() is called in defer
	})
}

// Helper function to compare heading maps
func equalHeadings(a, b map[string]int) bool {
	if len(a) != len(b) {
		return false
	}
	for k, v := range a {
		if b[k] != v {
			return false
		}
	}
	return true
}
