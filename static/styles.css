* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }
  
  .top-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
  
  header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
  }
  
  header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
  }
  
  header p {
    font-size: 1.1rem;
    opacity: 0.9;
  }
  
  main {
    flex: 1;
    background: #fff;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  .analyzer-form-panel,
  .analyzer-main-panel {
    margin-bottom: 30px;
  }
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
  }
  
  .input-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  input[type="url"] {
    flex: 1;
    min-width: 300px;
    padding: 12px 16px;
    font-size: 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    transition: border-color 0.3s ease;
  }
  
  input[type="url"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
  
  button {
    padding: 12px 24px;
    min-width: 120px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    background: #667eea;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  button:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
  }
  
  button:active {
    transform: translateY(0);
  }
  
  button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
  }
  
  /* Result Section */
  .results-section {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 2px solid #f0f0f0;
  }
  
  .results-section h2 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 20px;
  }
  
  .result-panel {
    display: grid;
    gap: 20px;
  }
  
  .result-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.3s ease;
  }
  
  .result-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .result-card h3 {
    font-size: 1.2rem;
    color: #495057;
    margin-bottom: 10px;
  }
  
  .result-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #667eea;
  }
  
  .headings-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 10px;
    margin-top: 10px;
  }
  
  .heading-item {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    text-align: center;
    padding: 8px;
  }
  
  .heading-level {
    font-weight: bold;
    color: #495057;
  }
  
  .heading-count {
    font-size: 1.2rem;
    font-weight: 600;
    color: #667eea;
  }
  
  .links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 10px;
  }
  
  .link-row {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    text-align: center;
    padding: 15px;
  }
  
  .link-row-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 5px;
  }
  
  .link-row-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
  }
  
  .login-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
  }
  
  .login-indicator.has-login {
    background: #d4edda;
    color: #155724;
  }
  
  .login-indicator.no-login {
    background: #f8d7da;
    color: #721c24;
  }
  
  .error-section {
    margin-top: 30px;
  }
  
  .error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
  }
  
  footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
  }
  
  @media (max-width: 768px) {
    .top-container {
      padding: 15px;
    }
  
    header h1 {
      font-size: 2rem;
    }
  
    main {
      padding: 25px;
    }
  
    .input-group {
      flex-direction: column;
    }
  
    input[type="url"] {
      min-width: 100%;
    }
  
    .headings-breakdown {
      grid-template-columns: repeat(3, 1fr);
    }
  
    .links-grid {
      grid-template-columns: 1fr;
    }
  }
  
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  .analyze-loading-icon::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
  }
  