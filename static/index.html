<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Page Analyzer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="top-container">
        <header>
            <h1>Web Page Analyzer</h1>
            <p>Analyze your web page to get details about it</p>
        </header>

        <main>
            <section class="analyzer-form-panel">

                <form id="analyzerForm">

                    <div class="analyzer-main-panel">
                        <label for="urlInputField">Enter URL to analyze:</label>
                        <div class="input-group">
                            <input 
                                type="url" 
                                id="urlInputField" 
                                name="url" 
                                placeholder="https://example.com"
                                required
                                autocomplete="url"
                            >
                            <button type="submit" id="analyzeBtn">
                                <span class="analyze-button-text">Analyze</span>
                                <span class="analyze-loading-icon" style="display: none;">Analyzing...</span>
                            </button>
                        </div>
                    </div>

                </form>

            </section>

            <section class="results-section" id="resultsSection" style="display: none;">
                <h2>Analysis Results</h2>
                <div class="result-panel" id="resultsContainer">
                    
                </div>
            </section>

            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-message" id="errorMessage">
                    
                </div>
            </section>
        </main>

    </div>

    <script src="script.js"></script>
</body>
</html>
